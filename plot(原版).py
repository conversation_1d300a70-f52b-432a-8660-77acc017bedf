import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class plot():
    # 绘制期权宣传图 - 模块化版本
    def __init__(self, kwargs):
        self.origin_x = kwargs["origin_x"]
        self.origin_y = kwargs["origin_y"]
        self.distance_x_arrow = kwargs["distance_x_arrow"] # x轴箭头距离页边的尺寸
        self.distance_y_arrow = kwargs["distance_y_arrow"] # y轴箭头距离页边的尺寸
        if kwargs["payoff"] == "vanilla_call":
            self.strike_point = self.origin_x + 0.25 # 行权价格点
        elif kwargs["payoff"] == "vanilla_put":
            self.strike_point = self.origin_x - 0.25 # 行权价格点
        self.initial_point = self.origin_x  # 期初价格点
        self.fixed_return = self.origin_y  # 固定收益率水平
        self.fig, self.ax = plt.subplots(figsize=(8, 6))

    def plot_coordinate_axis(self):
        """
        绘制坐标轴
        """
        # 完全隐藏所有默认坐标轴
        self.ax.spines['top'].set_visible(False)
        self.ax.spines['right'].set_visible(False)
        self.ax.spines['left'].set_visible(False)
        self.ax.spines['bottom'].set_visible(False)
        self.ax.set_xticks([])
        self.ax.set_yticks([])
        self.ax.set_xlim(0, 2)
        self.ax.set_ylim(0, 1.2)
        
        self.ax.annotate('', xy=(2-self.distance_x_arrow, self.origin_y), xytext=(2-self.distance_x_arrow-0.07, self.origin_y),
                arrowprops=dict(arrowstyle='->', color='black', lw=2.5, mutation_scale=10))
        self.ax.plot([0, 2-self.distance_x_arrow-0.05], [self.origin_y, self.origin_y], color='black', linewidth=2.5)
        self.ax.annotate('', xy=(self.origin_x, 1.2-self.distance_y_arrow), xytext=(self.origin_x, 1.2-self.distance_y_arrow-0.07),
                arrowprops=dict(arrowstyle='->', color='black', lw=2.5, mutation_scale=10))
        self.ax.plot([self.origin_x, self.origin_x], [0.0, 1.2-self.distance_y_arrow-0.05], color='black', linewidth=2.5)
        return None
    
    def plot_payoff(self):
        """
        绘制收益曲线
        """
        if kwargs["payoff"] == "vanilla_call":
            x = np.linspace(self.origin_x-0.5, self.origin_x+0.6, 1000)
            payoff = np.where(x > self.strike_point,
                        self.fixed_return + (x - self.strike_point) * 1,  # 看跌期权收益部分
                        self.fixed_return)  # 固定收益率部分
        elif kwargs["payoff"] == "vanilla_put":
            x = np.linspace(self.origin_x-0.6, self.origin_x+0.5, 1000)
            payoff = np.where(x < self.strike_point,
                     self.fixed_return + (self.strike_point - x) * 1,  # 看跌期权收益部分
                     self.fixed_return)  # 固定收益率部分
        self.ax.plot(x, payoff, color='red', linewidth=2.5)
    
    def plot_Commentary(self):
        """
        绘制注释
        """
        self.ax.text(2-0.08, self.origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
            ha='right', va='top', color='black')
        self.ax.text(self.origin_x+0.12, 1.2-0.1, '损益', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
            ha='right', va='bottom', color='black')
        
        self.ax.plot(self.strike_point, self.origin_y, 'ro', markersize=7) 
        self.ax.plot(self.initial_point, self.origin_y, 'ro', markersize=7)

        if kwargs["payoff"] == "vanilla_call":
            self.ax.text(self.strike_point+0.05, self.origin_y-0.02, '行权价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
                ha='center', va='top', color='black')
            self.ax.text(self.initial_point+0.1, self.origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
                ha='center', va='top', color='black')
        elif kwargs["payoff"] == "vanilla_put":
            self.ax.text(self.strike_point-0.02, self.origin_y-0.02, '行权价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
                ha='center', va='top', color='black')
            self.ax.text(self.initial_point-0.1, self.origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
                ha='center', va='top', color='black')
        self.fig.patch.set_facecolor('white')
        self.ax.set_facecolor('white')
        self.ax.set_title('')
        self.ax.set_xlabel('')
        self.ax.set_ylabel('')
        plt.tight_layout()
        return self.fig, self.ax

if __name__ == "__main__":
    kwargs = {
        "origin_x":1,
        "origin_y":0.5,
        "distance_x_arrow":0.02,
        "distance_y_arrow":0.02,
        "payoff":"vanilla_call"
    }
    Plot = plot(kwargs)
    Plot.plot_coordinate_axis()
    Plot.plot_payoff()
    fig,ax = Plot.plot_Commentary()
    plt.savefig('Call_1.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()

    kwargs = {
        "origin_x":1,
        "origin_y":0.5,
        "distance_x_arrow":0.02,
        "distance_y_arrow":0.02,
        "payoff":"vanilla_put"
    }
    Plot = plot(kwargs)
    Plot.plot_coordinate_axis()
    Plot.plot_payoff()
    fig,ax = Plot.plot_Commentary()
    plt.savefig('Put_1.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    print("欧式香草期权宣传图已生成完成！")
