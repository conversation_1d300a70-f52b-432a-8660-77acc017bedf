#include <iostream>
#include <vector>
using namespace std;

int main() {
    int h;
    cin >> h;
    vector<vector<int>> grid(h, vector<int>(5));

    // 读取输入
    for (int i = 0; i < h; i++) {
        for (int j = 0; j < 5; j++) {
            cin >> grid[i][j];
        }
    }

    int totalSum = 0;

    while (true) {
        vector<vector<bool>> toRemove(h, vector<bool>(5, false));

        // 标记水平连续3个以上相同数字
        for (int i = 0; i < h; i++) {
            for (int j = 0; j < 5; ) {
                if (grid[i][j] == 0) { j++; continue; }

                int start = j, count = 1;
                while (j + 1 < 5 && grid[i][j] == grid[i][j + 1]) {
                    count++; j++;
                }

                if (count >= 3) {
                    for (int k = start; k <= j; k++) {
                        toRemove[i][k] = true;
                    }
                }
                j++;
            }
        }

        // 检查是否有可消除的
        bool hasRemovable = false;
        for (int i = 0; i < h && !hasRemovable; i++) {
            for (int j = 0; j < 5; j++) {
                if (toRemove[i][j]) { hasRemovable = true; break; }
            }
        }
        if (!hasRemovable) break;

        // 消除并累加分数
        for (int i = 0; i < h; i++) {
            for (int j = 0; j < 5; j++) {
                if (toRemove[i][j]) {
                    totalSum += grid[i][j];
                    grid[i][j] = 0;
                }
            }
        }

        // 下落处理
        for (int j = 0; j < 5; j++) {
            vector<int> col;
            for (int i = 0; i < h; i++) {
                if (grid[i][j] != 0) col.push_back(grid[i][j]);
            }
            for (int i = 0; i < h; i++) {
                grid[i][j] = (i < h - col.size()) ? 0 : col[i - (h - col.size())];
            }
        }
    }

    cout << totalSum << endl;
    return 0;
}
