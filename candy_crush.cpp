#include <iostream>
#include <vector>
using namespace std;

class CandyCrush {
private:
    vector<vector<int>> grid;
    int rows, cols;
    
public:
    CandyCrush(int h) : rows(h), cols(5) {
        grid.resize(rows, vector<int>(cols));
    }
    
    // 读取输入
    void readInput() {
        for (int i = 0; i < rows; i++) {
            for (int j = 0; j < cols; j++) {
                cin >> grid[i][j];
            }
        }
    }
    
    // 标记需要消除的位置
    vector<vector<bool>> markToRemove() {
        vector<vector<bool>> toRemove(rows, vector<bool>(cols, false));
        
        // 检查水平方向
        for (int i = 0; i < rows; i++) {
            for (int j = 0; j < cols; ) {
                if (grid[i][j] == 0) {
                    j++;
                    continue;
                }
                
                int count = 1;
                int start = j;
                
                // 计算连续相同数字的长度
                while (j + 1 < cols && grid[i][j] == grid[i][j + 1]) {
                    count++;
                    j++;
                }
                
                // 如果连续3个或以上，标记为需要消除
                if (count >= 3) {
                    for (int k = start; k <= j; k++) {
                        toRemove[i][k] = true;
                    }
                }
                j++;
            }
        }
        
        return toRemove;
    }
    
    // 执行消除操作，返回消除的数字总和
    int removeMarked(const vector<vector<bool>>& toRemove) {
        int sum = 0;
        for (int i = 0; i < rows; i++) {
            for (int j = 0; j < cols; j++) {
                if (toRemove[i][j]) {
                    sum += grid[i][j];
                    grid[i][j] = 0;  // 设为0表示空位
                }
            }
        }
        return sum;
    }
    
    // 处理下落
    void applyGravity() {
        for (int j = 0; j < cols; j++) {
            // 对每一列进行下落处理
            vector<int> column;
            
            // 收集非零元素
            for (int i = 0; i < rows; i++) {
                if (grid[i][j] != 0) {
                    column.push_back(grid[i][j]);
                }
            }
            
            // 重新填充列，非零元素在下方
            for (int i = 0; i < rows; i++) {
                if (i < rows - column.size()) {
                    grid[i][j] = 0;
                } else {
                    grid[i][j] = column[i - (rows - column.size())];
                }
            }
        }
    }
    
    // 检查是否还有可以消除的
    bool hasRemovable() {
        vector<vector<bool>> toRemove = markToRemove();
        for (int i = 0; i < rows; i++) {
            for (int j = 0; j < cols; j++) {
                if (toRemove[i][j]) {
                    return true;
                }
            }
        }
        return false;
    }
    
    // 主要的消除逻辑
    int solve() {
        int totalSum = 0;
        
        while (true) {
            // 标记需要消除的位置
            vector<vector<bool>> toRemove = markToRemove();
            
            // 如果没有可消除的，结束
            if (!hasRemovable()) {
                break;
            }
            
            // 执行消除并累加分数
            totalSum += removeMarked(toRemove);
            
            // 应用重力下落
            applyGravity();
        }
        
        return totalSum;
    }
};

int main() {
    int h;
    cin >> h;
    
    CandyCrush game(h);
    game.readInput();
    
    int result = game.solve();
    cout << result << endl;
    
    return 0;
}
