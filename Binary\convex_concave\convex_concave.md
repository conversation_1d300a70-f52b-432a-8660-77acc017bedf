# 二元凹凸式

---

二元凹凸式式(Binary Concave/Convex Option)期权是一种有两个执行价格构成上下界，如果到期日的标的资产价格在上下界之内或之外，自动支付行权收益的期权。

二元凹凸式期权的期权类型分为两种：

二元凸式期权：在到期日，如果标的资产价格在上下界之内则触发行权，期权的买方获得行权收益；如果标的资产价格在上下界之外，则无法获得行权收益。

二元凹式期权：在到期日，如果标的资产价格在上下界之外则触发行权，期权的买方获得行权收益；如果标的资产价格在上下界之内，则无法获得行权收益。

因此，二元凹凸式期权的收益由到期日标的资产价格是否在上下界之内，期权类型以及行权收益率决定。

---

## 结构信息
| 字段 | 类型 |                 说明                |
|:---:|:---:|:------------------------------------:|
| 期权类型 | 下拉框 |              凸式/凹式             |
| 参与率 | 百分比 |        参与率的数值决定了实际名义本金的数额       |
| 行权价1 | 百分比 | 向下行权价，$行权价格1=期初价格\times行权价1$ |
| 行权价2 | 百分比 | 向上行权价，$行权价格2=期初价格\times行权价2$ |
| 行权收益率 | 百分比 |  期权在行权时，可获得固定数额资金回报的比例  |
| 期权费率 | 百分比 | 询价要素，如果是否年化选择True则为年化期权费，是否年化选择False则为绝对期权费 |

---

## 收益计算
 - 二元凸式
    $$
    \begin{aligned}
    收益\left(年化\right) &= \left(行权收益率\times\mathbf{1}_{\{行权价1 \leq 期末价格 \leq 行权价2\}}\times参与率+固定收益率\right)\\
    &\quad\times名义本金\times计算天数\div年化天数
    \end{aligned}
    $$
    $$
    \begin{aligned}
    收益\left(非年化\right) &= \left(行权收益率\times\mathbf{1}_{\{行权价1 \leq 期末价格 \leq 行权价2\}}\times参与率+固定收益率\right)\\
    &\quad\times名义本金
    \end{aligned}
    $$
 - 二元凹式
    $$
    \begin{aligned}
    收益\left(年化\right) &= \left(行权收益率\times\mathbf{1}_{\{期末价格 \geq 行权价2\ \text{或}\ 期末价格 \leq 行权价1\}}\times参与率+固定收益率\right)\\
    &\quad\times名义本金\times计算天数\div年化天数
    \end{aligned}
    $$
    $$
    \begin{aligned}
    收益\left(非年化\right) &= \left(行权收益率\times\mathbf{1}_{\{期末价格 \geq 行权价2\ \text{或}\ 期末价格 \leq 行权价1\}}\times参与率+固定收益率\right)\\
    &\quad\times名义本金
    \end{aligned}
    $$

---

## 收益示意图
 - 二元凸式  
  
    <img src="./binary_convex.png" width = "395" height = "295" alt="二元凸式收益图" />
 - 二元凹式  
  
    <img src="./binary_concave.png" width = "395" height = "295" alt="二元凹式收益图" />

---
