from pathlib import Path
from matplotlib.figure import Figure
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import PathPatch

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def gen_canvas() -> Figure:
    """
    生成画布
    画布包含 X轴 Y轴 原点等不变要素
    """
    fig, ax = plt.subplots(figsize=(8, 6))
    origin_x = 1
    origin_y = 0.5
    distance_x_arrow = 0.02
    distance_y_arrow = 0.02

    # 完全隐藏所有默认坐标轴
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_visible(False)
    ax.spines['bottom'].set_visible(False)
    ax.set_xticks([])
    ax.set_yticks([])
    ax.set_xlim(0, 2)
    ax.set_ylim(0, 1.2)

    ax.annotate('', xy=(2-distance_x_arrow, origin_y), xytext=(2-distance_x_arrow-0.07, origin_y),
            arrowprops=dict(arrowstyle='->', color='black', lw=2.5, mutation_scale=10))
    ax.plot([0, 2-distance_x_arrow-0.05], [origin_y, origin_y], color='black', linewidth=2.5)
    ax.annotate('', xy=(origin_x, 1.2-distance_y_arrow), xytext=(origin_x, 1.2-distance_y_arrow-0.07),
            arrowprops=dict(arrowstyle='->', color='black', lw=2.5, mutation_scale=10))
    ax.plot([origin_x, origin_x], [0.0, 1.2-distance_y_arrow-0.05], color='black', linewidth=2.5)
    ax.text(origin_x+0.12, 1.2-0.1, '损益', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='bottom', color='black')

    fig.patch.set_facecolor('white')
    ax.set_facecolor('white')
    ax.set_title('')
    ax.set_xlabel('')
    ax.set_ylabel('')
    return fig

"""
香草期权及其组合
"""

def add_vanilla_call_payoff(canvas: Figure) -> Figure:
    """
    传入一个画布
    在此函数内定义需要添加的元素
    不同Option对应不同函数
    """
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    strike_point = origin_x + 0.25  # 行权价格点
    initial_point = origin_x  # 期初价格点
    fixed_return = origin_y  # 固定收益率水平

    # 绘制看涨期权收益曲线
    x = np.linspace(origin_x-0.5, origin_x+0.6, 1000)
    payoff = np.where(x > strike_point,
                fixed_return + (x - strike_point) * 1,  # 看涨期权收益部分
                fixed_return)  # 固定收益率部分
    ax.plot(x, payoff, color='red', linewidth=2.5)

    ax.plot(strike_point, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)

    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(strike_point+0.05, origin_y-0.02, '行权价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point+0.1, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    plt.tight_layout()
    return canvas

def add_vanilla_put_payoff(canvas: Figure) -> Figure:
    """
    传入一个画布
    在此函数内定义看跌期权的收益曲线
    """
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    strike_point = origin_x - 0.25  # 行权价格点
    initial_point = origin_x  # 期初价格点
    fixed_return = origin_y  # 固定收益率水平

    # 绘制看跌期权收益曲线
    x = np.linspace(origin_x-0.6, origin_x+0.5, 1000)
    payoff = np.where(x < strike_point,
             fixed_return + (strike_point - x) * 1,  # 看跌期权收益部分
             fixed_return)  # 固定收益率部分
    ax.plot(x, payoff, color='red', linewidth=2.5)

    ax.plot(strike_point, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)

    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(strike_point-0.02, origin_y-0.02, '行权价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point-0.1, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    plt.tight_layout()
    return canvas

def add_american_call_payoff(canvas: Figure) -> Figure: 
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    strike_point = origin_x + 0.25  # 行权价格点
    initial_point = origin_x  # 期初价格点
    fixed_return = origin_y  # 固定收益率水平

    # 绘制看涨期权收益曲线
    x = np.linspace(origin_x-0.5, origin_x+0.6, 1000)
    payoff = np.where(x > strike_point,
                fixed_return + (x - strike_point) * 1,  # 看涨期权收益部分
                fixed_return)  # 固定收益率部分
    ax.plot(x, payoff, color='red', linewidth=2.5)

    ax.plot(strike_point, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)

    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(strike_point+0.05, origin_y-0.02, '行权价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point+0.1, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    plt.tight_layout()
    return canvas

def add_american_put_payoff(canvas: Figure) -> Figure: 
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    strike_point = origin_x - 0.25  # 行权价格点
    initial_point = origin_x  # 期初价格点
    fixed_return = origin_y  # 固定收益率水平

    # 绘制看跌期权收益曲线
    x = np.linspace(origin_x-0.6, origin_x+0.5, 1000)
    payoff = np.where(x < strike_point,
             fixed_return + (strike_point - x) * 1,  # 看跌期权收益部分
             fixed_return)  # 固定收益率部分
    ax.plot(x, payoff, color='red', linewidth=2.5)

    ax.plot(strike_point, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)

    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(strike_point-0.02, origin_y-0.02, '行权价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point-0.1, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    plt.tight_layout()
    return canvas    

def add_spread_call_payoff(canvas: Figure) -> Figure: 
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    strike_point2 = origin_x + 0.4  # 行权价格2
    strike_point1 = origin_x # 行权价格1
    initial_point = origin_x  # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.4 # 高于价差时的固定收益率水平

    # 绘制欧式价差看涨期权收益曲线
    x = np.linspace(origin_x-0.6, origin_x+0.8, 1000)
    payoff = np.piecewise(x,
        condlist=[x < strike_point1, 
              (x >= strike_point1) & (x <= strike_point2),
              x > strike_point2],
        funclist=[lambda x: fixed_return1,  # 低于strike_point1
              lambda x: fixed_return1 + (x - strike_point1) * 1,  # 区间内
              lambda x: fixed_return2] # 高于strike_point2
    )  
    ax.plot(x, payoff, color='red', linewidth=2.5)

    ax.plot(strike_point2, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot([strike_point2, strike_point2], [origin_y, origin_y + 0.4],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)

    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(strike_point2-0.02, origin_y-0.02, '行权价2', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point-0.1, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point1+0.1, origin_y-0.02, '行权价1', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    plt.tight_layout()
    return canvas   

def add_spread_put_payoff(canvas: Figure) -> Figure: 
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    strike_point2 = origin_x # 行权价格2
    strike_point1 = origin_x - 0.4 # 行权价格1
    initial_point = origin_x  # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.4 # 低于价差时的固定收益率水平

    # 绘制欧式价差看跌期权收益曲线
    x = np.linspace(origin_x-0.8, origin_x+0.6, 1000)
    payoff = np.piecewise(x,
        condlist=[x > strike_point2, 
              (x >= strike_point1) & (x <= strike_point2),
              x < strike_point1],
        funclist=[lambda x: fixed_return1,  # 高于strike_point1
              lambda x: fixed_return1 + (strike_point2 - x) * 1,  # 区间内
              lambda x: fixed_return2] # 低于strike_point2
    )  
    ax.plot(x, payoff, color='red', linewidth=2.5)

    ax.plot(strike_point1, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot([strike_point1, strike_point1], [origin_y, origin_y + 0.4],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)

    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(strike_point2-0.1, origin_y-0.02, '行权价2', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point+0.1, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point1-0.02, origin_y-0.02, '行权价1', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    plt.tight_layout()
    return canvas 

"""
障碍期权
"""

def barrier_Up_and_Out_Call_payoff(canvas: Figure) -> Figure:
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    out_point = origin_x + 0.6 # 敲出价格
    strike_point = origin_x + 0.2 # 行权价格
    initial_point = origin_x  # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.2 # 敲出时的固定收益率水平

    # 绘制向上敲出看涨期权收益曲线
    x = np.linspace(origin_x - 0.5, origin_x + 0.9, 1000)
    # 将条件拆分为连续区间（排除out_point的断点）
    condition1 = x < strike_point
    condition2 = (x >= strike_point) & (x <= out_point - 1e-6)  # 微调避免包含边界点
    condition3 = x > out_point + 1e-6  # 微调避免包含边界点

    # 分段计算收益
    payoff1 = np.where(condition1, fixed_return1, 0)
    payoff2 = np.where(condition2, fixed_return1 + (x - strike_point) * 1, 0)
    payoff3 = np.where(condition3, fixed_return2, 0)
    payoff = payoff1 + payoff2 + payoff3

    # 绘制三条连续的线段（避免垂直线）
    ax.plot(x[condition1], payoff[condition1], 'r-', lw=2.5)
    ax.plot(x[condition2], payoff[condition2], 'r-', lw=2.5)
    ax.plot(x[condition3], payoff[condition3], 'r-', lw=2.5)

    condition4 = (x > out_point + 1e-6) & (x < out_point + 0.12)
    payoff_other = np.where(condition4, fixed_return1 + (x - strike_point) * 1, 0)
    ax.plot(x[condition4], payoff_other[condition4], color='steelblue', linestyle='--', lw=2.5)

    ax.plot(strike_point, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot(out_point, origin_y, 'ro', markersize=7)
    ax.plot([out_point, out_point], [origin_y, origin_y + 0.4],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([initial_point, out_point], [fixed_return2, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([initial_point, out_point], [origin_y + 0.4, origin_y + 0.4],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    
    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(initial_point-0.1, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point, origin_y-0.02, '行权价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(out_point, origin_y-0.02, '敲出价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point+0.11, fixed_return2+0.05, '敲出补偿', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')

    plt.tight_layout()
    return canvas

def barrier_Down_and_Out_Put_payoff(canvas: Figure) -> Figure:
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    out_point = origin_x - 0.7 # 敲出价格
    strike_point = origin_x - 0.3 # 行权价格
    initial_point = origin_x # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.2 # 敲出时的固定收益率水平

    # 绘制向上敲出看涨期权收益曲线
    x = np.linspace(origin_x - 0.95, origin_x + 0.5, 1000)
    # 将条件拆分为连续区间（排除out_point的断点）
    condition1 = x > strike_point
    condition2 = (x <= strike_point) & (x >= out_point + 1e-6)  # 微调避免包含边界点
    condition3 = x < out_point - 1e-6  # 微调避免包含边界点

    # 分段计算收益
    payoff1 = np.where(condition1, fixed_return1, 0)
    payoff2 = np.where(condition2, fixed_return1 + (strike_point - x) * 1, 0)
    payoff3 = np.where(condition3, fixed_return2, 0)
    payoff = payoff1 + payoff2 + payoff3

    # 绘制三条连续的线段（避免垂直线）
    ax.plot(x[condition1], payoff[condition1], 'r-', lw=2.5)
    ax.plot(x[condition2], payoff[condition2], 'r-', lw=2.5)
    ax.plot(x[condition3], payoff[condition3], 'r-', lw=2.5)

    condition4 = (x < out_point + 1e-6) & (x > out_point - 0.12)
    payoff_other = np.where(condition4, fixed_return1 + (strike_point - x) * 1, 0)
    ax.plot(x[condition4], payoff_other[condition4], color='steelblue', linestyle='--', lw=2.5)

    ax.plot(strike_point, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot(out_point, origin_y, 'ro', markersize=7)
    ax.plot([out_point, out_point], [origin_y, origin_y + 0.4],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([initial_point, out_point], [fixed_return2, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([initial_point, out_point], [origin_y + 0.4, origin_y + 0.4],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    
    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(initial_point-0.1, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point, origin_y-0.02, '行权价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(out_point, origin_y-0.02, '敲出价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point-0.11, fixed_return2-0.02, '敲出补偿', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')

    plt.tight_layout()
    return canvas

def barrier_Up_and_In_Call_payoff(canvas: Figure) -> Figure:
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    out_point = origin_x + 0.6 # 敲出价格
    strike_point = origin_x + 0.2 # 行权价格
    initial_point = origin_x # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.2 # 敲出时的固定收益率水平

    # 绘制向上敲出看涨期权收益曲线
    x = np.linspace(origin_x - 0.5, origin_x + 0.9, 1000)
    # 将条件拆分为连续区间（排除out_point的断点）
    condition1 = (x < strike_point) & (x > initial_point) 
    condition2 = (x >= strike_point) & (x <= out_point + 1e-6)  # 微调避免包含边界点
    condition3 = (x > initial_point) & (x < out_point - 1e-6)  # 微调避免包含边界点

    # 分段计算收益
    payoff1 = np.where(condition1, fixed_return1, 0)
    payoff2 = np.where(condition2, fixed_return1 + (x - strike_point) * 1, 0)
    payoff3 = np.where(condition3, fixed_return2, 0)

    # 绘制三条连续的线段（避免垂直线）
    ax.plot(x[condition1], payoff1[condition1], color='steelblue', linestyle='--', lw=2.5)
    ax.plot(x[condition2], payoff2[condition2], color='steelblue', linestyle='--', lw=2.5)
    ax.plot(x[condition3], payoff3[condition3], 'r-', lw=2.5)

    condition4 = (x > out_point + 1e-6) & (x < out_point + 0.2)
    payoff_other = np.where(condition4, fixed_return1 + (x - strike_point) * 1, 0)
    ax.plot(x[condition4], payoff_other[condition4], 'r-', lw=2.5)

    ax.plot(strike_point, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot(out_point, origin_y, 'ro', markersize=7)
    ax.plot([out_point, out_point], [origin_y, origin_y + 0.4],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([initial_point, out_point], [origin_y + 0.4, origin_y + 0.4],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([initial_point, out_point], [origin_y + 0.4, origin_y + 0.4],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    
    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(initial_point-0.1, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point, origin_y-0.02, '行权价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(out_point, origin_y-0.02, '敲入价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point+0.14, fixed_return2+0.05, '未敲入补偿', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')

    plt.tight_layout()
    return canvas

def barrier_Down_and_In_Put_payoff(canvas: Figure) -> Figure:
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    out_point = origin_x - 0.6 # 敲出价格
    strike_point = origin_x - 0.2 # 行权价格
    initial_point = origin_x # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.2 # 敲出时的固定收益率水平

    # 绘制向上敲出看涨期权收益曲线
    x = np.linspace(origin_x - 0.9, origin_x + 0.5, 1000)
    # 将条件拆分为连续区间（排除out_point的断点）
    condition1 = (x > strike_point) & ( x < initial_point)
    condition2 = (x <= strike_point) & (x >= out_point + 1e-6)  # 微调避免包含边界点
    condition3 = (x > out_point + 1e-6) & (x < initial_point)  # 微调避免包含边界点 

    # 分段计算收益
    payoff1 = np.where(condition1, fixed_return1, 0)
    payoff2 = np.where(condition2, fixed_return1 + (strike_point - x) * 1, 0)
    payoff3 = np.where(condition3, fixed_return2, 0)

    # 绘制三条连续的线段（避免垂直线）
    ax.plot(x[condition1], payoff1[condition1], color='steelblue', linestyle='--', lw=2.5)
    ax.plot(x[condition2], payoff2[condition2], color='steelblue', linestyle='--', lw=2.5)
    ax.plot(x[condition3], payoff3[condition3], 'r-', lw=2.5)

    condition4 = (x < out_point - 1e-6) & (x > out_point - 0.2)
    payoff_other = np.where(condition4, fixed_return1 + (strike_point - x) * 1, 0)
    ax.plot(x[condition4], payoff_other[condition4], 'r-', lw=2.5)

    ax.plot(strike_point, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot(out_point, origin_y, 'ro', markersize=7)
    ax.plot([out_point, out_point], [origin_y, origin_y + 0.4],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([initial_point, out_point], [origin_y + 0.4, origin_y + 0.4],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    
    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(initial_point+0.1, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point, origin_y-0.02, '行权价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(out_point, origin_y-0.02, '敲入价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point-0.15, fixed_return2-0.02, '未敲入补偿', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')

    plt.tight_layout()
    return canvas

def barrier_Up_and_In_Put_payoff(canvas: Figure) -> Figure:
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    out_point = origin_x + 0.2 # 敲出价格
    strike_point = origin_x + 0.6 # 行权价格
    initial_point = origin_x # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.2 # 敲入时的固定收益率水平

    # 绘制向上敲出看涨期权收益曲线
    x = np.linspace(origin_x - 0.5, origin_x + 0.9, 1000)
    # 将条件拆分为连续区间（排除out_point的断点）
    condition1 = x > strike_point
    condition2 = (x <= strike_point) & (x >= out_point + 1e-6)  # 微调避免包含边界点
    condition3 = x < out_point - 1e-6  # 微调避免包含边界点

    # 分段计算收益
    payoff1 = np.where(condition1, fixed_return1, 0)
    payoff2 = np.where(condition2, fixed_return1 + (strike_point - x) * 1, 0)
    payoff3 = np.where(condition3, fixed_return2, 0)
    payoff = payoff1 + payoff2 + payoff3

    # 绘制三条连续的线段（避免垂直线）
    ax.plot(x[condition1], payoff[condition1], 'r-', lw=2.5)
    ax.plot(x[condition2], payoff[condition2], 'r-', lw=2.5)
    ax.plot(x[condition3], payoff[condition3], 'r-', lw=2.5)

    condition4 = (x<out_point - 1e-6) & (x>initial_point)
    payoff4 = np.where(condition4, fixed_return1 + (strike_point - x) * 1, 0)
    ax.plot(x[condition4], payoff4[condition4], color='steelblue', linestyle='--', lw=2.5)

    ax.plot(strike_point, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot(out_point, origin_y, 'ro', markersize=7)
    ax.plot([out_point, out_point], [origin_y, origin_y + 0.4],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    
    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(initial_point-0.1, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point, origin_y-0.02, '行权价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(out_point, origin_y-0.02, '敲入价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point-0.14, fixed_return2-0.02, '未敲入补偿', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')

    plt.tight_layout()
    return canvas

def barrier_Up_and_Out_Put_payoff(canvas: Figure) -> Figure:
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    out_point = origin_x + 0.2 # 敲出价格
    strike_point = origin_x + 0.6 # 行权价格
    initial_point = origin_x # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.2 # 敲出时的固定收益率水平

    # 绘制向上敲出看涨期权收益曲线
    x = np.linspace(origin_x - 0.5, origin_x + 0.9, 1000)
    # 将条件拆分为连续区间（排除out_point的断点）
    condition1 = x > strike_point
    condition2 = (x <= strike_point) & (x >= out_point + 1e-6)  # 微调避免包含边界点
    condition3 = (x < out_point - 1e-6) & (x > initial_point) # 微调避免包含边界点

    # 分段计算收益
    payoff1 = np.where(condition1, fixed_return1, 0)
    payoff2 = np.where(condition2, fixed_return1 + (strike_point - x) * 1, 0)
    payoff3 = np.where(condition3, fixed_return1 + (strike_point - x) * 1, 0)
    payoff = payoff1 + payoff2 + payoff3

    # 绘制三条连续的线段（避免垂直线）
    ax.plot(x[condition1], payoff[condition1], color='steelblue', linestyle='--', lw=2.5)
    ax.plot(x[condition2], payoff[condition2], color='steelblue', linestyle='--', lw=2.5)
    ax.plot(x[condition3], payoff[condition3], 'r-', lw=2.5)

    condition4 = (x>out_point + 1e-6) & (x < origin_x + 0.8)
    payoff4 = np.where(condition4, fixed_return2, 0)
    ax.plot(x[condition4], payoff4[condition4], 'r-', lw=2.5)

    ax.plot(strike_point, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot(out_point, origin_y, 'ro', markersize=7)
    ax.plot([out_point, out_point], [origin_y, origin_y + 0.4],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([initial_point, out_point], [fixed_return2, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)

    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(initial_point-0.1, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point, origin_y-0.02, '行权价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(out_point, origin_y-0.02, '敲出价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point-0.11, fixed_return2-0.02, '敲出补偿', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    plt.tight_layout()
    return canvas

def barrier_Down_and_In_Call_payoff(canvas: Figure) -> Figure:
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    out_point = origin_x - 0.2 # 敲入价格
    strike_point = origin_x - 0.6 # 行权价格
    initial_point = origin_x # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.2 # 敲出时的固定收益率水平

    # 绘制向上敲出看涨期权收益曲线
    x = np.linspace(origin_x - 0.9, origin_x + 0.5, 1000)
    # 将条件拆分为连续区间（排除out_point的断点）
    condition1 = x < strike_point
    condition2 = (x >= strike_point) & (x <= out_point + 1e-6)  # 微调避免包含边界点
    condition3 = (x > out_point - 1e-6) & (x < origin_x + 0.4) # 微调避免包含边界点

    # 分段计算收益
    payoff1 = np.where(condition1, fixed_return1, 0)
    payoff2 = np.where(condition2, fixed_return1 + (x - strike_point) * 1, 0)
    payoff3 = np.where(condition3, fixed_return2, 0)
    payoff = payoff1 + payoff2 + payoff3

    # 绘制三条连续的线段（避免垂直线）
    ax.plot(x[condition1], payoff[condition1], 'r-', lw=2.5)
    ax.plot(x[condition2], payoff[condition2], 'r-', lw=2.5)
    ax.plot(x[condition3], payoff[condition3], 'r-', lw=2.5)

    condition4 = (x > out_point + 1e-6) & (x < initial_point)
    payoff4 = np.where(condition4, fixed_return1 + (x - strike_point) * 1, 0)
    ax.plot(x[condition4], payoff4[condition4], color='steelblue', linestyle='--', lw=2.5)

    ax.plot(strike_point, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot(out_point, origin_y, 'ro', markersize=7)
    ax.plot([out_point, out_point], [origin_y, origin_y + 0.4],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)

    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(initial_point+0.1, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point, origin_y-0.02, '行权价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(out_point, origin_y-0.02, '敲入价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point+0.14, fixed_return2-0.02, '未敲入补偿', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    plt.tight_layout()
    return canvas

def barrier_Down_and_Out_Call_payoff(canvas: Figure) -> Figure:
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    out_point = origin_x - 0.2 # 敲入价格
    strike_point = origin_x - 0.6 # 行权价格
    initial_point = origin_x # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.2 # 敲出时的固定收益率水平

    x = np.linspace(origin_x - 0.9, origin_x + 0.5, 1000)
    # 将条件拆分为连续区间（排除out_point的断点）
    condition1 = x < strike_point
    condition2 = (x >= strike_point) & (x <= out_point + 1e-6)  # 微调避免包含边界点
    condition3 = (x < out_point - 1e-6) & (x > out_point - 0.7) # 微调避免包含边界点

    # 分段计算收益
    payoff1 = np.where(condition1, fixed_return1, 0)
    payoff2 = np.where(condition2, fixed_return1 + (x - strike_point) * 1, 0)
    payoff3 = np.where(condition3, fixed_return2, 0)

    # 绘制三条连续的线段（避免垂直线）
    ax.plot(x[condition1], payoff1[condition1], color='steelblue', linestyle='--', lw=2.5)
    ax.plot(x[condition2], payoff2[condition2], color='steelblue', linestyle='--', lw=2.5)
    ax.plot(x[condition3], payoff3[condition3], 'r-', lw=2.5)

    condition4 = (x > out_point + 1e-6) & (x < initial_point)
    payoff4 = np.where(condition4, fixed_return1 + (x - strike_point) * 1, 0)
    ax.plot(x[condition4], payoff4[condition4], 'r-', lw=2.5)

    ax.plot(strike_point, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot(out_point, origin_y, 'ro', markersize=7)
    ax.plot([out_point, out_point], [origin_y, origin_y + 0.4],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([out_point, initial_point], [fixed_return2, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)

    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(initial_point+0.1, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point, origin_y-0.02, '行权价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(out_point, origin_y-0.02, '敲出价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point+0.11, fixed_return2-0.02, '敲出补偿', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    plt.tight_layout()
    return canvas    

def Double_Barrier_payoff(canvas: Figure) -> Figure:
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    out_point1 = origin_x - 0.5 # 敲出价格1
    out_point2 = origin_x + 0.6 # 敲出价格2
    strike_point2 = origin_x + 0.2 # 行权价格2
    strike_point1 = origin_x - 0.2 # 行权价格1
    initial_point = origin_x # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.2 # 敲出补偿收益率1
    fixed_return3 = origin_y + 0.3 # 敲出补偿收益率1

    x = np.linspace(origin_x - 0.9, origin_x + 0.9, 1000)
    # 将条件拆分为连续区间（排除out_point的断点）
    condition1 = (x>out_point1) & (x < strike_point1)
    condition2 = (x >= strike_point1) & (x <= strike_point2)
    condition3 = (x < out_point2) & (x > strike_point2) 

    payoff1 = np.where(condition1, fixed_return1 + (strike_point1 - x) * 1, 0)
    payoff2 = np.where(condition2, fixed_return1 , 0)
    payoff3 = np.where(condition3, fixed_return1 + (x - strike_point2) * 1, 0)

    condition4 = (x<out_point1) & (x>out_point1 - 0.25)
    condition5 = (x>out_point2) & (x<out_point2 + 0.25)
    payoff4 = np.where(condition4, fixed_return2, 0)
    payoff5 = np.where(condition5, fixed_return3, 0)
    
    condition6 = (x<out_point1) & (x>out_point1 - 0.12)
    condition7 = (x>out_point2) & (x<out_point2 + 0.12)
    payoff6 = np.where(condition6, fixed_return1 + (strike_point1 - x) * 1, 0)
    payoff7 = np.where(condition7, fixed_return1 + (x - strike_point2) * 1, 0)
    
    ax.plot(x[condition1], payoff1[condition1], 'r-', lw=2.5)
    ax.plot(x[condition2], payoff2[condition2], 'r-', lw=2.5)
    ax.plot(x[condition3], payoff3[condition3], 'r-', lw=2.5)
    ax.plot(x[condition4], payoff4[condition4], 'r-', lw=2.5)
    ax.plot(x[condition5], payoff5[condition5], 'r-', lw=2.5)
    ax.plot(x[condition6], payoff6[condition6], color='steelblue', linestyle='--', lw=2.5)
    ax.plot(x[condition7], payoff7[condition7], color='steelblue', linestyle='--', lw=2.5)

    ax.plot(strike_point1, origin_y, 'ro', markersize=7)
    ax.plot(strike_point2, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot(out_point1, origin_y, 'ro', markersize=7)
    ax.plot(out_point2, origin_y, 'ro', markersize=7)
    ax.plot([out_point1, out_point1], [origin_y, origin_y + 0.3],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([out_point2, out_point2], [origin_y, origin_y + 0.4],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([out_point1, initial_point], [fixed_return2, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([out_point2, initial_point], [fixed_return3, fixed_return3],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    
    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(initial_point+0.08, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point1, origin_y-0.02, '行权价1', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point2+0.08, origin_y-0.02, '行权价2', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(out_point1, origin_y-0.02, '敲出价1', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(out_point2, origin_y-0.02, '敲出价2', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point-0.14, fixed_return2-0.02, '敲出补偿1', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point+0.14, fixed_return3-0.02, '敲出补偿2', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    plt.tight_layout()
    return canvas    

"""
二元及其组合类型
"""

def Digital_call_payoff(canvas: Figure) -> Figure:
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    strike_point1 = origin_x + 0.3 # 行权价格1
    initial_point = origin_x # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.2 # 行权收益率

    x = np.linspace(origin_x - 0.5, origin_x + 0.9, 1000)
    condition1 = (x < strike_point1)
    condition2 = (x >= strike_point1)

    payoff1 = np.where(condition1, fixed_return1, 0)
    payoff2 = np.where(condition2, fixed_return2 , 0)

    ax.plot(x[condition1], payoff1[condition1], 'r-', lw=2.5)
    ax.plot(x[condition2], payoff2[condition2], 'r-', lw=2.5)

    ax.plot(strike_point1, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot([strike_point1, strike_point1], [origin_y, origin_y + 0.2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([initial_point, strike_point1], [fixed_return2, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    
    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(initial_point-0.08, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point1, origin_y-0.02, '行权价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point+0.11, fixed_return2+0.05, '行权收益', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')

    plt.tight_layout()
    return canvas    


def Digital_put_payoff(canvas: Figure) -> Figure:
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    strike_point1 = origin_x - 0.3 # 行权价格1
    initial_point = origin_x # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.2 # 行权收益率

    x = np.linspace(origin_x - 0.9, origin_x + 0.5, 1000)
    condition1 = (x < strike_point1)
    condition2 = (x >= strike_point1)

    payoff1 = np.where(condition1, fixed_return2, 0)
    payoff2 = np.where(condition2, fixed_return1 , 0)

    ax.plot(x[condition1], payoff1[condition1], 'r-', lw=2.5)
    ax.plot(x[condition2], payoff2[condition2], 'r-', lw=2.5)

    ax.plot(strike_point1, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot([strike_point1, strike_point1], [origin_y, origin_y + 0.2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([initial_point, strike_point1], [fixed_return2, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    
    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(initial_point+0.08, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point1, origin_y-0.02, '行权价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point-0.11, fixed_return2+0.05, '行权收益', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')

    plt.tight_layout()
    return canvas    

def binary_convex_payoff(canvas: Figure) -> Figure: 
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    strike_point1 = origin_x - 0.25 # 行权价格1
    strike_point2 = origin_x + 0.25 # 行权价格2
    initial_point = origin_x # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.2 # 行权收益率

    x = np.linspace(origin_x - 0.6, origin_x + 0.6, 1000)
    condition1 = (x < strike_point1)
    condition2 = (x >= strike_point2)
    condition3 = (x >= strike_point1) & (x < strike_point2)

    payoff = np.where(condition3, fixed_return2, fixed_return1)
    ax.plot(x[condition1], payoff[condition1], 'r-', lw=2.5)
    ax.plot(x[condition2], payoff[condition2], 'r-', lw=2.5)
    ax.plot(x[condition3], payoff[condition3], 'r-', lw=2.5)

    ax.plot(strike_point1, origin_y, 'ro', markersize=7)
    ax.plot(strike_point2, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot([strike_point1, strike_point1], [origin_y, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([strike_point2, strike_point2], [origin_y, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    
    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(initial_point+0.08, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point1, origin_y-0.02, '行权价1', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point2+0.05, origin_y-0.02, '行权价2', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point+0.11, fixed_return2+0.05, '行权收益', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')

    plt.tight_layout()
    return canvas 

def binary_concave_payoff(canvas: Figure) -> Figure:
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    strike_point1 = origin_x - 0.25 # 行权价格1
    strike_point2 = origin_x + 0.25 # 行权价格2
    initial_point = origin_x # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.2 # 行权收益率

    x = np.linspace(origin_x - 0.6, origin_x + 0.6, 1000)
    condition1 = (x < strike_point1)
    condition2 = (x >= strike_point2)
    condition3 = (x >= strike_point1) & (x < strike_point2)

    payoff = np.where(condition3, fixed_return1, fixed_return2)
    ax.plot(x[condition1], payoff[condition1], 'r-', lw=2.5)
    ax.plot(x[condition2], payoff[condition2], 'r-', lw=2.5)
    ax.plot(x[condition3], payoff[condition3], 'r-', lw=2.5)

    ax.plot(strike_point1, origin_y, 'ro', markersize=7)
    ax.plot(strike_point2, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot([strike_point1, strike_point1], [origin_y, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([strike_point2, strike_point2], [origin_y, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([strike_point1, strike_point2], [fixed_return2, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    
    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(initial_point+0.08, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point1, origin_y-0.02, '行权价1', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point2+0.05, origin_y-0.02, '行权价2', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point+0.11, fixed_return2+0.05, '行权收益', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    plt.tight_layout()
    return canvas 

def One_touch_call_payoff(canvas: Figure) -> Figure:
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    strike_point1 = origin_x + 0.3 # 行权价格1
    initial_point = origin_x # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.2 # 行权收益率

    x = np.linspace(origin_x - 0.5, origin_x + 0.9, 1000)
    condition1 = (x < strike_point1)
    condition2 = (x >= strike_point1)

    payoff1 = np.where(condition1, fixed_return1, 0)
    payoff2 = np.where(condition2, fixed_return2 , 0)

    ax.plot(x[condition1], payoff1[condition1], 'r-', lw=2.5)
    ax.plot(x[condition2], payoff2[condition2], 'r-', lw=2.5)

    ax.plot(strike_point1, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot([strike_point1, strike_point1], [origin_y, origin_y + 0.2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([initial_point, strike_point1], [fixed_return2, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    
    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(initial_point-0.08, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point1, origin_y-0.02, '行权价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point+0.11, fixed_return2+0.05, '行权收益', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')

    plt.tight_layout()
    return canvas    

def One_touch_put_payoff(canvas: Figure) -> Figure:
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    strike_point1 = origin_x - 0.3 # 行权价格1
    initial_point = origin_x # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.2 # 行权收益率

    x = np.linspace(origin_x - 0.9, origin_x + 0.5, 1000)
    condition1 = (x < strike_point1)
    condition2 = (x >= strike_point1)

    payoff1 = np.where(condition1, fixed_return2, 0)
    payoff2 = np.where(condition2, fixed_return1 , 0)

    ax.plot(x[condition1], payoff1[condition1], 'r-', lw=2.5)
    ax.plot(x[condition2], payoff2[condition2], 'r-', lw=2.5)

    ax.plot(strike_point1, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot([strike_point1, strike_point1], [origin_y, origin_y + 0.2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([initial_point, strike_point1], [fixed_return2, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    
    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(initial_point+0.08, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point1, origin_y-0.02, '行权价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point-0.11, fixed_return2+0.05, '行权收益', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')

    plt.tight_layout()
    return canvas    

def Double_one_touch_payoff(canvas: Figure) -> Figure:
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    strike_point1 = origin_x - 0.25 # 行权价格1
    strike_point2 = origin_x + 0.25 # 行权价格2
    initial_point = origin_x # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.2 # 行权收益率1
    fixed_return3 = origin_y + 0.3 # 行权收益率2

    x = np.linspace(origin_x - 0.6, origin_x + 0.6, 1000)
    condition1 = (x < strike_point1)
    condition2 = (x >= strike_point2)
    condition3 = (x >= strike_point1) & (x < strike_point2)

    payoff1 = np.where(condition2, fixed_return3, 0)
    payoff2 = np.where(condition3, fixed_return1, 0)
    payoff3 = np.where(condition1, fixed_return2, 0)
    payoff = payoff1 + payoff2 + payoff3

    ax.plot(x[condition1], payoff[condition1], 'r-', lw=2.5)
    ax.plot(x[condition2], payoff[condition2], 'r-', lw=2.5)
    ax.plot(x[condition3], payoff[condition3], 'r-', lw=2.5)

    ax.plot(strike_point1, origin_y, 'ro', markersize=7)
    ax.plot(strike_point2, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot([strike_point1, strike_point1], [origin_y, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([strike_point2, strike_point2], [origin_y, fixed_return3],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([strike_point1, initial_point], [fixed_return2, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([strike_point2, initial_point], [fixed_return3, fixed_return3],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    
    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(initial_point+0.08, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point1, origin_y-0.02, '行权价1', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point2+0.05, origin_y-0.02, '行权价2', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point-0.13, fixed_return2+0.05, '行权收益1', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point+0.13, fixed_return3+0.05, '行权收益2', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    plt.tight_layout()
    return canvas 

def Double_no_touch_payoff(canvas: Figure) -> Figure: 
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    strike_point1 = origin_x - 0.25 # 行权价格1
    strike_point2 = origin_x + 0.25 # 行权价格2
    initial_point = origin_x # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.2 # 行权收益率

    x = np.linspace(origin_x - 0.6, origin_x + 0.6, 1000)
    condition1 = (x < strike_point1)
    condition2 = (x >= strike_point2)
    condition3 = (x >= strike_point1) & (x < strike_point2)

    payoff = np.where(condition3, fixed_return2, fixed_return1)
    ax.plot(x[condition1], payoff[condition1], 'r-', lw=2.5)
    ax.plot(x[condition2], payoff[condition2], 'r-', lw=2.5)
    ax.plot(x[condition3], payoff[condition3], 'r-', lw=2.5)

    ax.plot(strike_point1, origin_y, 'ro', markersize=7)
    ax.plot(strike_point2, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot([strike_point1, strike_point1], [origin_y, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([strike_point2, strike_point2], [origin_y, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    
    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(initial_point+0.08, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point1, origin_y-0.02, '行权价1', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point2+0.05, origin_y-0.02, '行权价2', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point+0.11, fixed_return2+0.05, '行权收益', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')

    plt.tight_layout()
    return canvas 

def Ladder_call_payoff(canvas: Figure) -> Figure:
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    strike_point1 = origin_x + 0.3 # 行权价格1
    strike_point2 = origin_x + 0.6 # 行权价格2
    initial_point = origin_x # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.2 # 行权收益率1
    fixed_return3 = origin_y + 0.4 # 行权收益率2

    x = np.linspace(origin_x - 0.5, origin_x + 0.9, 1000)
    condition1 = (x < strike_point1)
    condition2 = (x >= strike_point1) & (x < strike_point2)
    condition3 = (x >= strike_point2)

    payoff1 = np.where(condition1, fixed_return1, 0)
    payoff2 = np.where(condition2, fixed_return2, 0)
    payoff3 = np.where(condition3, fixed_return3, 0)
    payoff = payoff1 + payoff2 + payoff3

    ax.plot(x[condition1], payoff[condition1], 'r-', lw=2.5)
    ax.plot(x[condition2], payoff[condition2], 'r-', lw=2.5)
    ax.plot(x[condition3], payoff[condition3], 'r-', lw=2.5)

    ax.plot(strike_point1, origin_y, 'ro', markersize=7)
    ax.plot(strike_point2, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot([strike_point1, strike_point1], [origin_y, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([strike_point2, strike_point2], [origin_y, fixed_return3],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([strike_point1, initial_point], [fixed_return2, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([strike_point2, initial_point], [fixed_return3, fixed_return3],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    
    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(initial_point-0.08, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point1, origin_y-0.02, '行权价1', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point2, origin_y-0.02, '行权价2', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point+0.13, fixed_return2+0.05, '行权收益1', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point+0.13, fixed_return3+0.05, '行权收益2', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    plt.tight_layout()
    return canvas 

def Ladder_put_payoff(canvas: Figure) -> Figure:
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    strike_point1 = origin_x - 0.3 # 行权价格1
    strike_point2 = origin_x - 0.6 # 行权价格2
    initial_point = origin_x # 期初价格点
    fixed_return1 = origin_y  # 固定收益率水平
    fixed_return2 = origin_y + 0.2 # 行权收益率1
    fixed_return3 = origin_y + 0.4 # 行权收益率2

    x = np.linspace(origin_x - 0.9, origin_x + 0.5, 1000)
    condition1 = (x > strike_point1)
    condition2 = (x <= strike_point1) & (x > strike_point2)
    condition3 = (x <= strike_point2)

    payoff1 = np.where(condition1, fixed_return1, 0)
    payoff2 = np.where(condition2, fixed_return2, 0)
    payoff3 = np.where(condition3, fixed_return3, 0)
    payoff = payoff1 + payoff2 + payoff3

    ax.plot(x[condition1], payoff[condition1], 'r-', lw=2.5)
    ax.plot(x[condition2], payoff[condition2], 'r-', lw=2.5)
    ax.plot(x[condition3], payoff[condition3], 'r-', lw=2.5)

    ax.plot(strike_point1, origin_y, 'ro', markersize=7)
    ax.plot(strike_point2, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot([strike_point1, strike_point1], [origin_y, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([strike_point2, strike_point2], [origin_y, fixed_return3],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([strike_point1, initial_point], [fixed_return2, fixed_return2],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([strike_point2, initial_point], [fixed_return3, fixed_return3],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    
    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(initial_point+0.08, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point1, origin_y-0.02, '行权价1', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point2, origin_y-0.02, '行权价2', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point+0.13, fixed_return2+0.05, '行权收益1', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(initial_point+0.13, fixed_return3+0.05, '行权收益2', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    plt.tight_layout()
    return canvas 

def Airbag_call_payoff(canvas: Figure) -> Figure:
    ax = canvas.axes[0]
    origin_x = 1
    origin_y = 0.5
    strike_point1 = origin_x + 0.1 # 行权价格1
    initial_point = origin_x # 期初价格点
    out_point = origin_x - 0.2 # 敲入价格点
    fixed_return1 = origin_y  # 固定收益率水平
    slope1 = 1 - 0.4 # 未敲入参与率
    slope2 = 1 # 敲入上方参与率
    slope3 = 1 - 0.2 # 敲入下方参与率

    x = np.linspace(origin_x - 0.3, origin_x + 0.5, 1000)
    condition1 = (x > strike_point1)
    payoff1 = np.where(condition1, fixed_return1 + (x - strike_point1) * slope1, fixed_return1)
    payoff2 = np.where(condition1, fixed_return1 + (x - strike_point1) * slope2, fixed_return1 + (x - strike_point1)*slope3)

    ax.plot(x, payoff1, color='steelblue', linestyle='-', lw=2.5)
    ax.plot(x, payoff2, color='red', linestyle='-', lw=2.5)
    upper_red_annotation = (origin_x + 0.2, origin_y + 0.3)
    upper_red_point = (origin_x + 0.3, fixed_return1 + (origin_x + 0.3 - strike_point1) * slope2)
    ax.annotate('敲入上方参与率', xy=upper_red_point, xytext=upper_red_annotation,
                fontsize=12, fontweight='bold', fontfamily='Microsoft YaHei',
                ha='center', va='center', color='black',
                arrowprops=dict(arrowstyle='-', linestyle='--', color='black', alpha=0.7))
    downer_red_annotation = (origin_x - 0.4, origin_y - 0.2)
    downer_red_point = (0.8, fixed_return1 + (0.8 - strike_point1) * slope3)
    ax.annotate('敲入下方参与率', xy=downer_red_point, xytext=downer_red_annotation,
                fontsize=12, fontweight='bold', fontfamily='Microsoft YaHei',
                ha='center', va='center', color='black',
                arrowprops=dict(arrowstyle='-', linestyle='--', color='black', alpha=0.7))
    upper_blue_annotation = (origin_x + 0.62, origin_y + 0.2)
    upper_blue_point = (origin_x + 0.3, fixed_return1 + (origin_x + 0.3 - strike_point1) * slope1)
    ax.annotate('敲入上方参与率', xy=upper_blue_point, xytext=upper_blue_annotation,
                fontsize=12, fontweight='bold', fontfamily='Microsoft YaHei',
                ha='center', va='center', color='black',
                arrowprops=dict(arrowstyle='-', linestyle='--', color='black', alpha=0.7))
    # 在行权价和敲入价之间添加红色大括号
    bracket_y = origin_y - 0.08  # 大括号的y位置
    bracket_height = 0.03  # 大括号的高度

    # 绘制大括号的左侧垂直线
    ax.plot([out_point, out_point], [bracket_y - bracket_height/2, bracket_y + bracket_height/2],
            color='red', linewidth=2.5)

    # 绘制大括号的右侧垂直线
    ax.plot([strike_point1, strike_point1], [bracket_y - bracket_height/2, bracket_y + bracket_height/2],
            color='red', linewidth=2.5)

    # 绘制大括号的水平连接线
    ax.plot([out_point, strike_point1], [bracket_y, bracket_y],
            color='red', linewidth=2.5)

    # 绘制大括号中间的小突起
    bracket_center_x = (out_point + strike_point1) / 2
    ax.plot([bracket_center_x, bracket_center_x], [bracket_y, bracket_y - bracket_height/3],
            color='red', linewidth=2.5)

    ax.plot(strike_point1, origin_y, 'ro', markersize=7)
    ax.plot(initial_point, origin_y, 'ro', markersize=7)
    ax.plot(out_point, origin_y, 'ro', markersize=7)

    ax.plot([origin_x + 0.5, origin_x + 0.5], [origin_y, origin_y + 0.4],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    ax.plot([origin_x, origin_x + 0.5], [origin_y + 0.4, origin_y + 0.4],
            color='grey', linestyle='--', linewidth=2.5, alpha=0.7)
    
    ax.text(2-0.08, origin_y-0.02, '标的价格', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='right', va='top', color='black')
    ax.text(initial_point-0.08, origin_y-0.02, '期初价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(strike_point1+0.05, origin_y-0.02, '行权价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')
    ax.text(out_point-0.08, origin_y-0.02, '敲入价', fontsize=14, fontweight='bold', fontfamily='Microsoft YaHei',
        ha='center', va='top', color='black')

    plt.tight_layout()
    return canvas

def image_scaling(target, new_size: tuple[int], output_path: str | Path):
    """
    传入图片和新比例
    输出图片到指定路径
    """
    pass

if __name__ == "__main__":
    # 生成看涨期权图
    # canvas_call = gen_canvas()
    # canvas_call = add_american_call_payoff(canvas_call)
    # plt.savefig('american_call.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show()

    # 生成看跌期权图
    # canvas_put = gen_canvas()
    # canvas_put = add_american_put_payoff(canvas_put)
    # plt.savefig('american_put.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show()

    # canvas_spread_call = gen_canvas()
    # canvas_spread_call = add_spread_call_payoff(canvas_spread_call)
    # plt.savefig('spread_call.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show()

    # canvas_spread_put = gen_canvas()
    # canvas_spread_put = add_spread_put_payoff(canvas_spread_put)
    # plt.savefig('spread_put.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show()

    # canvas_barrier = gen_canvas()
    # canvas_barrier = barrier_Up_and_Out_Call_payoff(canvas_barrier)
    # plt.savefig('Up_and_Out_Call.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show()

    # canvas_barrier = gen_canvas()
    # canvas_barrier = barrier_Down_and_Out_Put_payoff(canvas_barrier)
    # plt.savefig('Down_and_Out_Put.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show()

    # canvas_barrier = gen_canvas()
    # canvas_barrier = barrier_Up_and_In_Call_payoff(canvas_barrier)
    # plt.savefig('Up_and_In_Call.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show()

    # canvas_barrier = gen_canvas()
    # canvas_barrier = barrier_Down_and_In_Put_payoff(canvas_barrier)
    # plt.savefig('Down_and_In_Put.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show()

    # canvas_barrier = gen_canvas()
    # canvas_barrier = barrier_Up_and_In_Put_payoff(canvas_barrier)
    # plt.savefig('Up_and_In_Put.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show()
    
    # canvas_barrier = gen_canvas()
    # canvas_barrier = barrier_Up_and_Out_Put_payoff(canvas_barrier)
    # plt.savefig('Up_and_Out_Put.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show()

    # canvas_barrier = gen_canvas()
    # canvas_barrier = barrier_Down_and_In_Call_payoff(canvas_barrier)
    # plt.savefig('Down_and_In_Call.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show()

    # canvas_barrier = gen_canvas()
    # canvas_barrier = barrier_Down_and_In_Call_payoff(canvas_barrier)
    # plt.savefig('Down_and_In_Call.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show()    

    # canvas_barrier = gen_canvas()
    # canvas_barrier = barrier_Down_and_Out_Call_payoff(canvas_barrier)
    # plt.savefig('Down_and_Out_Call.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show() 

    # canvas_barrier = gen_canvas()
    # canvas_barrier = Double_Barrier_payoff(canvas_barrier)
    # plt.savefig('Double_Barrier2.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show() 

    # canvas_barrier = gen_canvas()
    # canvas_barrier = Digital_call_payoff(canvas_barrier)
    # plt.savefig('Digital_call.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show() 

    # canvas_barrier = gen_canvas()
    # canvas_barrier = Digital_put_payoff(canvas_barrier)
    # plt.savefig('Digital_put.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show() 

    # canvas_barrier = gen_canvas()
    # canvas_barrier = binary_convex_payoff(canvas_barrier)
    # plt.savefig('binary_convex.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show() 

    # canvas_barrier = gen_canvas()
    # canvas_barrier = binary_concave_payoff(canvas_barrier)
    # plt.savefig('binary_concave.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show() 

    # canvas_barrier = gen_canvas()
    # canvas_barrier = One_touch_call_payoff(canvas_barrier)
    # plt.savefig('One_touch_call.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show() 
    
    # canvas_barrier = gen_canvas()
    # canvas_barrier = One_touch_put_payoff(canvas_barrier)
    # plt.savefig('One_touch_put.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show() 

    # canvas_barrier = gen_canvas()
    # canvas_barrier = Double_one_touch_payoff(canvas_barrier)
    # plt.savefig('Double_one_touch.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show() 

    # canvas_barrier = gen_canvas()
    # canvas_barrier = Double_no_touch_payoff(canvas_barrier)
    # plt.savefig('Double_no_touch.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show() 

    # canvas_barrier = gen_canvas()
    # canvas_barrier = Ladder_call_payoff(canvas_barrier)
    # plt.savefig('Ladder_call.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show() 

    # canvas_barrier = gen_canvas()
    # canvas_barrier = Ladder_put_payoff(canvas_barrier)
    # plt.savefig('Ladder_put.png', dpi=300, bbox_inches='tight', facecolor='white')
    # plt.show() 

    canvas_barrier = gen_canvas()
    canvas_barrier = Airbag_call_payoff(canvas_barrier)
    plt.savefig('Airbag_call.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.show() 