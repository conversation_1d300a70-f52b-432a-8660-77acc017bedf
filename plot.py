from pathlib import Path
from matplotlib.figure import Figure

def gen_canvas() -> Figure:
    """
    生成画布
    画布包含 X轴 Y轴 原点等不变要素
    """
    pass

def add_vanilla_call_payoff(canvas: Figure) -> Figure:
    """
    传入一个画布
    在此函数内定义需要添加的元素
    不同Option对应不同函数
    """
    pass

def add_vanilla_put_payoff(canvas: Figure) -> Figure:
    
    pass

def add_american_payoff(canvas: Figure) -> Figure: ...

def add_binary_payoff(canvas: Figure) -> Figure: ...


def image_scaling(target: 'image', new_size: tuple[int], output_path: str | Path) -> Optional['image']:
    """
    传入图片和新比例
    输出图片到指定路径
    """
    pass
