# 阶梯期权

---

阶梯期权(Ladder option)是一种设置若干预先确定的行权价阶梯，在到期日前当标的资产价格向上或向下触及某个阶梯水平时，按照合约约定自动锁定部分行权收益，在到期日自动支付所有锁定的行权收益的期权。

阶梯期权的期权类型分为两种：

看涨期权：在到期日前，当标的资产价格向上突破某一阶梯水平时，按合约条款行权收益会被部分锁定，在到期日时期权的买方获得所有锁定的行权收益。如果标的资产价格未触及任一行权价，则无法获得行权收益。

看跌期权：在到期日前，当标的资产价格向下突破某一阶梯水平时，按合约条款行权收益会被部分锁定，在到期日时期权的买方获得所有锁定的行权收益。如果标的资产价格未触及任一行权价，则无法获得行权收益。

因此，阶梯期权的收益由到期日前标的资产价格是否触及预先设定的阶梯水平，期权类型及行权收益率决定。

---

## 结构信息
| 字段 | 类型 |              说明             |
|:---:|:---:|:-----------------------------:|
| 期权类型 | 下拉框 |            看涨/看跌            |
| 参与率 | 百分比 |    参与率的数值决定了实际名义本金的数额     |
| 阶梯行权价系数 | 下拉框 | 预先设定的阶梯水平，如果选择看涨是单调递增；如果选择看跌是单调递减 |
| 阶梯行权收益率 | 下拉框 | 预先设定的一系列行权收益率，决定期权在行权时可获得固定数额资金回报的比例 |
| 期权费率 | 百分比 | 询价要素，如果是否年化选择True则为年化期权费，是否年化选择False则为绝对期权费 |

---

## 收益计算
 - 阶梯看涨
    $$
    \begin{aligned}
    收益\left(年化\right) &= \left(\left(\sum_{i=1}^{2} 行权收益率_i \times\mathbf{1}_{\{期权价格 \geq 行权价_i\}}\right)\times参与率  + 固定收益率 \right) \\
    &\quad\times名义本金\times计算天数\div年化天数
    \end{aligned}
    $$
    $$
    \begin{aligned}
    收益\left(非年化\right) &= \left(\left(\sum_{i=1}^{2} 行权收益率_i \times\mathbf{1}_{\{期权价格 \geq 行权价_i\}}\right)\times参与率  + 固定收益率 \right) \\
    &\quad\times名义本金
    \end{aligned}
    $$
 - 阶梯看跌
    $$
    \begin{aligned}
    收益\left(年化\right) &= \left(\left(\sum_{i=1}^{2} 行权收益率_i \times\mathbf{1}_{\{期权价格 \leq 行权价_i\}}\right)\times参与率  + 固定收益率 \right) \\
    &\quad\times名义本金\times计算天数\div年化天数
    \end{aligned}
    $$
    $$
    \begin{aligned}
    收益\left(非年化\right) &= \left(\left(\sum_{i=1}^{2} 行权收益率_i \times\mathbf{1}_{\{期权价格 \leq 行权价_i\}}\right)\times参与率  + 固定收益率 \right) \\
    &\quad\times名义本金
    \end{aligned}
    $$

---

## 收益示意图
 - 阶梯看涨
    
    <img src="./Ladder_call.png" width = "395" height = "295" alt="阶梯看涨收益图"/>
 - 阶梯看跌
    
    <img src="./Ladder_put.png" width = "395" height = "295" alt="阶梯看跌收益图" />

---